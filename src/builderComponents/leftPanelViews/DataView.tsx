import { useMemo } from 'react'
import { Database, FileText, Plus, User, ShoppingCart } from 'lucide-react'

export const DataView = () => {
    return useMemo(() => (
        <div className="w-full h-full overflow-y-auto rounded-lg shadow-sm flex flex-col">
            <h3 className="m-0 text-base font-semibold p-3 border-b border-border/50 bg-muted/30">
                🗄️ Data & Database
            </h3>
            
            <div className="flex-1 overflow-y-auto">
                <div className="p-3">
                    <div className="flex items-center justify-between mb-3">
                        <div className="text-sm text-muted-foreground">Store and manage your content</div>
                        <button className="p-1 rounded-md hover:bg-muted/50 transition-colors">
                            <Plus className="h-4 w-4 text-muted-foreground" />
                        </button>
                    </div>
                    
                    <div className="space-y-2">
                        <div className="p-3 border border-dashed border-border/50 rounded-lg hover:bg-muted/30 transition-colors cursor-pointer">
                            <div className="flex items-center gap-3">
                                <User className="h-4 w-4 text-muted-foreground" />
                                <div className="flex-1">
                                    <div className="text-sm font-medium">Users</div>
                                    <div className="text-xs text-muted-foreground">User accounts and profiles</div>
                                </div>
                                <span className="text-xs text-muted-foreground bg-muted/50 px-2 py-1 rounded-full">
                                    5 fields
                                </span>
                            </div>
                        </div>
                        
                        <div className="p-3 border border-dashed border-border/50 rounded-lg hover:bg-muted/30 transition-colors cursor-pointer">
                            <div className="flex items-center gap-3">
                                <FileText className="h-4 w-4 text-muted-foreground" />
                                <div className="flex-1">
                                    <div className="text-sm font-medium">Blog Posts</div>
                                    <div className="text-xs text-muted-foreground">Articles and blog content</div>
                                </div>
                                <span className="text-xs text-muted-foreground bg-muted/50 px-2 py-1 rounded-full">
                                    8 fields
                                </span>
                            </div>
                        </div>
                        
                        <div className="p-3 border border-dashed border-border/50 rounded-lg hover:bg-muted/30 transition-colors cursor-pointer">
                            <div className="flex items-center gap-3">
                                <ShoppingCart className="h-4 w-4 text-muted-foreground" />
                                <div className="flex-1">
                                    <div className="text-sm font-medium">Products</div>
                                    <div className="text-xs text-muted-foreground">E-commerce products</div>
                                </div>
                                <span className="text-xs text-muted-foreground bg-muted/50 px-2 py-1 rounded-full">
                                    12 fields
                                </span>
                            </div>
                        </div>
                    </div>
                    
                    <div className="mt-4 p-3 border border-dashed border-border/30 rounded-lg text-center">
                        <Database className="h-6 w-6 text-muted-foreground mx-auto mb-2" />
                        <div className="text-sm text-muted-foreground">Create new collection</div>
                    </div>
                </div>
            </div>
        </div>
    ), [])
}
