import { useMemo } from 'react'
import { FileText, Layout, Plus } from 'lucide-react'

export const PagesView = () => {
    return useMemo(() => (
        <div className="w-full h-full overflow-y-auto rounded-lg shadow-sm flex flex-col">
            <h3 className="m-0 text-base font-semibold p-3 border-b border-border/50 bg-muted/30">
                📄 Pages
            </h3>
            
            <div className="flex-1 overflow-y-auto">
                <div className="p-3">
                    <div className="flex items-center justify-between mb-3">
                        <div className="text-sm text-muted-foreground">Manage your website pages</div>
                        <button className="p-1 rounded-md hover:bg-muted/50 transition-colors">
                            <Plus className="h-4 w-4 text-muted-foreground" />
                        </button>
                    </div>
                    
                    <div className="space-y-2">
                        <div className="p-3 border border-dashed border-border/50 rounded-lg hover:bg-muted/30 transition-colors cursor-pointer">
                            <div className="flex items-center gap-3">
                                <Layout className="h-4 w-4 text-muted-foreground" />
                                <div className="flex-1">
                                    <div className="text-sm font-medium">Home</div>
                                    <div className="text-xs text-muted-foreground">Main landing page</div>
                                </div>
                                <span className="text-xs text-green-600 bg-green-100 dark:bg-green-900/30 px-2 py-1 rounded-full">
                                    Published
                                </span>
                            </div>
                        </div>
                        
                        <div className="p-3 border border-dashed border-border/50 rounded-lg hover:bg-muted/30 transition-colors cursor-pointer">
                            <div className="flex items-center gap-3">
                                <FileText className="h-4 w-4 text-muted-foreground" />
                                <div className="flex-1">
                                    <div className="text-sm font-medium">About</div>
                                    <div className="text-xs text-muted-foreground">About us page</div>
                                </div>
                                <span className="text-xs text-yellow-600 bg-yellow-100 dark:bg-yellow-900/30 px-2 py-1 rounded-full">
                                    Draft
                                </span>
                            </div>
                        </div>
                        
                        <div className="p-3 border border-dashed border-border/50 rounded-lg hover:bg-muted/30 transition-colors cursor-pointer">
                            <div className="flex items-center gap-3">
                                <FileText className="h-4 w-4 text-muted-foreground" />
                                <div className="flex-1">
                                    <div className="text-sm font-medium">Contact</div>
                                    <div className="text-xs text-muted-foreground">Contact information</div>
                                </div>
                                <span className="text-xs text-gray-600 bg-gray-100 dark:bg-gray-900/30 px-2 py-1 rounded-full">
                                    Unpublished
                                </span>
                            </div>
                        </div>
                    </div>
                    
                    <div className="mt-4 p-3 border border-dashed border-border/30 rounded-lg text-center">
                        <Plus className="h-6 w-6 text-muted-foreground mx-auto mb-2" />
                        <div className="text-sm text-muted-foreground">Add new page</div>
                    </div>
                </div>
            </div>
        </div>
    ), [])
}
