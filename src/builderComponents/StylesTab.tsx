import { Key } from 'react'
import { JollyTextField } from '@/components/ui/textfield'
import { JollySelect, SelectItem } from '@/components/ui/select'
import { Slider, SliderFillTrack, SliderThumb, SliderTrack } from '@/components/ui/slider'
import { HexColorPicker } from '@/components/ui/hexColorPicker'
import { Disclosure, DisclosureHeader, DisclosurePanel } from '@/components/ui/disclosure'
import { BoxShadowPositionValue, } from '@/lib/builder/styleUtils'
import {
    Palette,
    Type,
    Layout,
    Space,
    Eye,
    Layers,
    Zap,
} from 'lucide-react'

type SpacingValues = {
    top: string;
    right: string;
    bottom: string;
    left: string;
};

type DisplayValue = 'block' | 'flex' | 'grid' | '';
type FlexDirectionValue = 'row' | 'column' | '';
type JustifyContentValue =
    'flex-start'
    | 'flex-end'
    | 'center'
    | 'space-between'
    | 'space-around'
    | 'space-evenly'
    | '';
type AlignItemsValue = 'start' | 'end' | 'center' | 'stretch' | '';
type FontWeightValue = 'normal' | 'bold' | '100' | '200' | '300' | '400' | '500' | '600' | '700' | '800' | '900' | '';
type FontStyleValue = 'normal' | 'italic' | '';
type BorderStyleValue = 'none' | 'solid' | 'dashed' | 'dotted' | 'double' | '';
type PositionValue = 'static' | 'relative' | 'absolute' | 'fixed' | 'sticky' | '';
type TextAlignValue = 'left' | 'center' | 'right' | 'justify' | '';

interface StylesTabProps {
    selectedElement?: HTMLElement;
    styleValues: {
        margin: SpacingValues;
        padding: SpacingValues;
        display: DisplayValue;
        gridTemplateColumns: string;
        gridTemplateRows: string;
        flexDirection: FlexDirectionValue;
        justifyContent: JustifyContentValue;
        alignItems: AlignItemsValue;
        gap: string;
        gapValue: number;
        width: string;
        height: string;
        maxWidth: string;
        maxHeight: string;
        fontWeight: FontWeightValue;
        fontSize: string;
        fontSizeValue: number;
        lineHeight: string;
        lineHeightValue: number;
        backgroundColor: string;
        borderRadius: string;
        borderRadiusValue: number;
        borderWidth: string;
        borderWidthValue: number;
        borderColor: string;
        borderStyle: BorderStyleValue;
        color: string;
        fontStyle: FontStyleValue;
        position: PositionValue;
        top: string;
        right: string;
        bottom: string;
        left: string;
        textAlign: TextAlignValue;
        boxShadow: string;
        boxShadowOffsetX: number;
        boxShadowOffsetY: number;
        boxShadowBlur: number;
        boxShadowSpread: number;
        boxShadowColor: string;
        boxShadowPosition: BoxShadowPositionValue;
    };
    activeInputs: {
        gridTemplateColumns: string;
        gridTemplateRows: string;
        gap: string;
        width: string;
        height: string;
        maxWidth: string;
        maxHeight: string;
        margin: SpacingValues;
        padding: SpacingValues;
        top: string;
        right: string;
        bottom: string;
        left: string;
    };
    displayMode: DisplayValue;
    updateStyle: (property: keyof CSSStyleDeclaration, value: string) => void;
    handleInputBlur: () => void;
    handleSpacingChange: (type: 'margin' | 'padding', direction: keyof SpacingValues, value: string) => void;
    handlePositionChange: (property: 'top' | 'right' | 'bottom' | 'left', value: string) => void;
    handleBoxShadowChange: (property: 'offsetX' | 'offsetY' | 'blur' | 'spread' | 'position', value: number | string) => void;
    handleSelectChange: (key: React.Key, property: string, styleProperty: keyof CSSStyleDeclaration) => void;
    handleSliderChange: (value: number, unit: string, valueProperty: string, styleProperty: keyof CSSStyleDeclaration) => void;
    debouncedColorChange: (property: string, value: string) => void;
    setActiveInputs: React.Dispatch<React.SetStateAction<any>>;
    styleValuesRef: React.MutableRefObject<any>;
    setRerender: React.Dispatch<React.SetStateAction<number>>;
}

const StylesTab = ({
    selectedElement,
    styleValues,
    activeInputs,
    displayMode,
    updateStyle,
    handleInputBlur,
    handleSpacingChange,
    handlePositionChange,
    handleBoxShadowChange,
    handleSelectChange,
    handleSliderChange,
    debouncedColorChange,
    setActiveInputs,
    styleValuesRef,
    setRerender
}: StylesTabProps) => {
    const {
        margin,
        padding,
        display,
        gridTemplateColumns,
        gridTemplateRows,
        flexDirection,
        justifyContent,
        alignItems,
        gap,
        gapValue,
        fontWeight,
        fontSize,
        fontSizeValue,
        lineHeight,
        lineHeightValue,
        backgroundColor,
        borderRadius,
        borderRadiusValue,
        borderWidth,
        borderWidthValue,
        borderColor,
        borderStyle,
        color,
        width,
        height,
        maxWidth,
        maxHeight,
        fontStyle,
        position,
        top,
        right,
        bottom,
        left,
        textAlign,
        boxShadow,
        boxShadowOffsetX,
        boxShadowOffsetY,
        boxShadowBlur,
        boxShadowSpread,
        boxShadowColor,
        boxShadowPosition
    } = styleValues

    const displayOptions = [
        {id: 'block', name: 'Block'},
        {id: 'flex', name: 'Flex'},
        {id: 'grid', name: 'Grid'},
    ]

    const flexDirectionOptions = [
        {id: 'row', name: 'Row'},
        {id: 'column', name: 'Column'},
    ]

    const justifyContentOptions = [
        {id: 'flex-start', name: 'Start'},
        {id: 'flex-end', name: 'End'},
        {id: 'center', name: 'Center'},
        {id: 'space-between', name: 'Space Between'},
        {id: 'space-around', name: 'Space Around'},
        {id: 'space-evenly', name: 'Space Evenly'},
    ]

    const alignItemsOptions = [
        {id: 'flex-start', name: 'Start'},
        {id: 'flex-end', name: 'End'},
        {id: 'center', name: 'Center'},
        {id: 'stretch', name: 'Stretch'},
        {id: 'baseline', name: 'Baseline'},
    ]

    const fontWeightOptions = [
        {id: 'normal', name: 'Normal'},
        {id: '100', name: 'Thin (100)'},
        {id: '200', name: 'Extra Light (200)'},
        {id: '300', name: 'Light (300)'},
        {id: '400', name: 'Regular (400)'},
        {id: '500', name: 'Medium (500)'},
        {id: '600', name: 'Semi Bold (600)'},
        {id: '700', name: 'Bold (700)'},
        {id: '800', name: 'Extra Bold (800)'},
        {id: '900', name: 'Black (900)'},
    ]

    const fontStyleOptions = [
        {id: 'normal', name: 'Normal'},
        {id: 'italic', name: 'Italic'},
    ]

    const borderStyleOptions = [
        {id: 'none', name: 'None'},
        {id: 'solid', name: 'Solid'},
        {id: 'dashed', name: 'Dashed'},
        {id: 'dotted', name: 'Dotted'},
        {id: 'double', name: 'Double'},
    ]

    const positionOptions = [
        {id: 'static', name: 'Static'},
        {id: 'relative', name: 'Relative'},
        {id: 'absolute', name: 'Absolute'},
        {id: 'fixed', name: 'Fixed'},
        {id: 'sticky', name: 'Sticky'},
    ]

    const textAlignOptions = [
        {id: 'left', name: 'Left'},
        {id: 'center', name: 'Center'},
        {id: 'right', name: 'Right'},
        {id: 'justify', name: 'Justify'},
    ]

    const boxShadowPositionOptions = [
        {id: 'outset', name: 'Outset (Drop Shadow)'},
        {id: 'inset', name: 'Inset (Inner Shadow)'},
    ]

    return (
        <div>
            {/* Theme Integration */}
            <Disclosure className="px-2 border-0 border-b border-b-secondary">
                <DisclosureHeader className="font-semibold flex items-center gap-2">
                    <Palette className="h-4 w-4" />
                    Theme Integration
                </DisclosureHeader>
                <DisclosurePanel className="pl-3">
                    <div className="flex flex-col gap-3">
                        <div>
                            <label className="text-sm font-medium">Color Palette</label>
                            <div className="grid grid-cols-4 gap-2 mt-2">
                                <button className="w-8 h-8 rounded-md bg-primary border border-border" title="Primary"></button>
                                <button className="w-8 h-8 rounded-md bg-secondary border border-border" title="Secondary"></button>
                                <button className="w-8 h-8 rounded-md bg-accent border border-border" title="Accent"></button>
                                <button className="w-8 h-8 rounded-md bg-muted border border-border" title="Muted"></button>
                                <button className="w-8 h-8 rounded-md bg-green-500 border border-border" title="Success"></button>
                                <button className="w-8 h-8 rounded-md bg-red-500 border border-border" title="Error"></button>
                                <button className="w-8 h-8 rounded-md bg-yellow-500 border border-border" title="Warning"></button>
                                <button className="w-8 h-8 rounded-md bg-blue-500 border border-border" title="Info"></button>
                            </div>
                        </div>

                        <JollySelect
                            label="Typography Scale"
                            aria-label="Typography Scale"
                            items={[
                                {id: 'custom', name: 'Custom'},
                                {id: 'xs', name: 'xs (12px)'},
                                {id: 'sm', name: 'sm (14px)'},
                                {id: 'base', name: 'base (16px)'},
                                {id: 'lg', name: 'lg (18px)'},
                                {id: 'xl', name: 'xl (20px)'},
                                {id: '2xl', name: '2xl (24px)'},
                                {id: '3xl', name: '3xl (30px)'},
                                {id: '4xl', name: '4xl (36px)'},
                            ]}
                            selectedKey="custom"
                            onSelectionChange={(key) => {
                                // Handle typography scale selection
                                console.log('Typography scale selected:', key)
                            }}
                        >
                            {(item) => <SelectItem id={item.id}>{item.name}</SelectItem>}
                        </JollySelect>

                        <div>
                            <label className="text-sm font-medium">Spacing Scale</label>
                            <select className="w-full mt-1 p-2 border border-border rounded-md text-sm">
                                <option>Custom</option>
                                <option>1 (4px)</option>
                                <option>2 (8px)</option>
                                <option>3 (12px)</option>
                                <option>4 (16px)</option>
                                <option>6 (24px)</option>
                                <option>8 (32px)</option>
                                <option>12 (48px)</option>
                                <option>16 (64px)</option>
                            </select>
                        </div>

                        <div>
                            <label className="text-sm font-medium">Border Radius</label>
                            <select className="w-full mt-1 p-2 border border-border rounded-md text-sm">
                                <option>Custom</option>
                                <option>none (0px)</option>
                                <option>sm (2px)</option>
                                <option>base (4px)</option>
                                <option>md (6px)</option>
                                <option>lg (8px)</option>
                                <option>xl (12px)</option>
                                <option>full (9999px)</option>
                            </select>
                        </div>
                    </div>
                </DisclosurePanel>
            </Disclosure>

            {/* Size & Dimensions */}
            <Disclosure defaultExpanded className="px-2 border-0 border-b border-b-secondary">
                <DisclosureHeader className="font-semibold flex items-center gap-2">
                    <Layout className="h-4 w-4" />
                    Size & Dimensions
                </DisclosureHeader>
                <DisclosurePanel className="pl-3">
                    <div className="flex flex-col gap-3">
                        <div className="grid grid-cols-2 gap-2">
                            <JollyTextField
                                label="Width"
                                aria-label="Width"
                                value={width}
                                onChange={(v) => {
                                    updateStyle('width', v)
                                    styleValuesRef.current.width = v
                                    setActiveInputs(prev => ({...prev, width: v}))
                                }}
                                onBlur={handleInputBlur}
                            />
                            <JollyTextField
                                label="Height"
                                aria-label="Height"
                                value={height}
                                onChange={(v) => {
                                    updateStyle('height', v)
                                    styleValuesRef.current.height = v
                                    setActiveInputs(prev => ({...prev, height: v}))
                                }}
                                onBlur={handleInputBlur}
                            />
                        </div>
                        <div className="grid grid-cols-2 gap-2">
                            <JollyTextField
                                label="Max width"
                                aria-label="Max width"
                                value={maxWidth}
                                onChange={(v) => {
                                    updateStyle('maxWidth', v)
                                    styleValuesRef.current.maxWidth = v
                                    setActiveInputs(prev => ({...prev, maxWidth: v}))
                                }}
                                onBlur={handleInputBlur}
                            />
                            <JollyTextField
                                label="Max height"
                                aria-label="Max height"
                                value={maxHeight}
                                onChange={(v) => {
                                    updateStyle('maxHeight', v)
                                    styleValuesRef.current.maxHeight = v
                                    setActiveInputs(prev => ({...prev, maxHeight: v}))
                                }}
                                onBlur={handleInputBlur}
                            />
                        </div>
                    </div>
                </DisclosurePanel>
            </Disclosure>

            {/* Layout */}
            <Disclosure defaultExpanded className="px-2 border-0 border-b border-b-secondary">
                <DisclosureHeader className="font-semibold flex items-center gap-2">
                    <Layers className="h-4 w-4" />
                    Layout
                </DisclosureHeader>
                <DisclosurePanel className="pl-3">
                    <div className="flex flex-col gap-4">
                        {/* General Layout */}
                        <div>
                            <h5 className="text-sm font-medium mb-2">General</h5>
                            <div className="flex flex-col gap-3">
                                <div className="grid grid-cols-2 gap-2">
                                    <JollySelect
                                        label="Display"
                                        aria-label="Display"
                                        items={displayOptions}
                                        selectedKey={display}
                                        onSelectionChange={(key) => handleSelectChange(key, 'display', 'display')}
                                    >
                                        {(item) => <SelectItem id={item.id}>{item.name}</SelectItem>}
                                    </JollySelect>

                                    <JollySelect
                                        label="Position"
                                        aria-label="Position"
                                        items={positionOptions}
                                        selectedKey={position}
                                        onSelectionChange={(key) => handleSelectChange(key, 'position', 'position')}
                                    >
                                        {(item) => <SelectItem id={item.id}>{item.name}</SelectItem>}
                                    </JollySelect>
                                </div>

                                <div>
                                    <JollyTextField
                                        label="Gap"
                                        aria-label="Gap"
                                        value={activeInputs.gap}
                                        onChange={(v) => {
                                            updateStyle('gap', v)
                                            styleValuesRef.current.gap = v
                                            setActiveInputs(prev => ({...prev, gap: v}))
                                        }}
                                        onBlur={handleInputBlur}
                                    />
                                    <div className="flex flex-col gap-1 mt-2">
                                        <label className="text-sm font-medium">Gap: {gapValue}px</label>
                                        <Slider
                                            value={gapValue}
                                            defaultValue={gapValue}
                                            onChange={(value) => handleSliderChange(value as number, 'px', 'gap', 'gap')}
                                            onChangeEnd={() => handleInputBlur()}
                                            minValue={0}
                                            maxValue={100}
                                            step={1}
                                            aria-label="Gap"
                                        >
                                            <SliderTrack>
                                                <SliderFillTrack/>
                                            </SliderTrack>
                                            <SliderThumb/>
                                        </Slider>
                                    </div>
                                </div>

                                {position !== 'static' && position !== '' && (
                                    <div className="grid grid-cols-2 gap-2">
                                        <JollyTextField
                                            label="Top"
                                            aria-label="Position Top"
                                            value={activeInputs.top}
                                            onChange={(v) => handlePositionChange('top', v)}
                                            onBlur={handleInputBlur}
                                        />
                                        <JollyTextField
                                            label="Right"
                                            aria-label="Position Right"
                                            value={activeInputs.right}
                                            onChange={(v) => handlePositionChange('right', v)}
                                            onBlur={handleInputBlur}
                                        />
                                        <JollyTextField
                                            label="Bottom"
                                            aria-label="Position Bottom"
                                            value={activeInputs.bottom}
                                            onChange={(v) => handlePositionChange('bottom', v)}
                                            onBlur={handleInputBlur}
                                        />
                                        <JollyTextField
                                            label="Left"
                                            aria-label="Position Left"
                                            value={activeInputs.left}
                                            onChange={(v) => handlePositionChange('left', v)}
                                            onBlur={handleInputBlur}
                                        />
                                    </div>
                                )}
                            </div>
                        </div>

                        {/* Flex Layout */}
                        {displayMode === 'flex' && (
                            <div>
                                <h5 className="text-sm font-medium mb-2">Flex Layout</h5>
                                <div className="flex flex-col gap-3">
                                    <JollySelect
                                        label="Flex Direction"
                                        aria-label="Flex Direction"
                                        items={flexDirectionOptions}
                                        selectedKey={flexDirection}
                                        onSelectionChange={(key) => handleSelectChange(key, 'flexDirection', 'flexDirection')}
                                    >
                                        {(item) => <SelectItem id={item.id}>{item.name}</SelectItem>}
                                    </JollySelect>
                                    <JollySelect
                                        label="Justify Content"
                                        aria-label="Justify Content"
                                        items={justifyContentOptions}
                                        selectedKey={justifyContent}
                                        onSelectionChange={(key) => handleSelectChange(key, 'justifyContent', 'justifyContent')}
                                    >
                                        {(item) => <SelectItem id={item.id}>{item.name}</SelectItem>}
                                    </JollySelect>
                                    <JollySelect
                                        label="Align Items"
                                        aria-label="Align Items"
                                        items={alignItemsOptions}
                                        selectedKey={alignItems}
                                        onSelectionChange={(key) => handleSelectChange(key, 'alignItems', 'alignItems')}
                                    >
                                        {(item) => <SelectItem id={item.id}>{item.name}</SelectItem>}
                                    </JollySelect>
                                </div>
                            </div>
                        )}

                        {/* Grid Layout */}
                        {displayMode === 'grid' && (
                            <div>
                                <h5 className="text-sm font-medium mb-2">Grid Layout</h5>
                                <div className="flex flex-col gap-3">
                                    <JollyTextField
                                        label="Grid Columns"
                                        aria-label="Grid Columns"
                                        value={activeInputs.gridTemplateColumns}
                                        onChange={(v) => {
                                            updateStyle('gridTemplateColumns', v)
                                            styleValuesRef.current.gridTemplateColumns = v
                                            setActiveInputs(prev => ({...prev, gridTemplateColumns: v}))
                                        }}
                                        onBlur={handleInputBlur}
                                    />
                                    <JollyTextField
                                        label="Grid Rows"
                                        aria-label="Grid Rows"
                                        value={activeInputs.gridTemplateRows}
                                        onChange={(v) => {
                                            updateStyle('gridTemplateRows', v)
                                            styleValuesRef.current.gridTemplateRows = v
                                            setActiveInputs(prev => ({...prev, gridTemplateRows: v}))
                                        }}
                                        onBlur={handleInputBlur}
                                    />
                                </div>
                            </div>
                        )}
                    </div>
                </DisclosurePanel>
            </Disclosure>

            {/* Spacing */}
            <Disclosure defaultExpanded className="px-2 border-0 border-b border-b-secondary">
                <DisclosureHeader className="font-semibold flex items-center gap-2">
                    <Space className="h-4 w-4" />
                    Spacing
                </DisclosureHeader>
                <DisclosurePanel className="pl-3">
                    <div className="flex flex-col gap-4">
                        <div>
                            <h5 className="text-sm font-medium mb-2">Margin</h5>
                            <div className="grid grid-cols-2 gap-2">
                                <JollyTextField
                                    label="Top"
                                    aria-label="Margin Top"
                                    value={activeInputs.margin.top}
                                    onChange={(v) => handleSpacingChange('margin', 'top', v)}
                                    onBlur={handleInputBlur}
                                />
                                <JollyTextField
                                    label="Right"
                                    aria-label="Margin Right"
                                    value={activeInputs.margin.right}
                                    onChange={(v) => handleSpacingChange('margin', 'right', v)}
                                    onBlur={handleInputBlur}
                                />
                                <JollyTextField
                                    label="Bottom"
                                    aria-label="Margin Bottom"
                                    value={activeInputs.margin.bottom}
                                    onChange={(v) => handleSpacingChange('margin', 'bottom', v)}
                                    onBlur={handleInputBlur}
                                />
                                <JollyTextField
                                    label="Left"
                                    aria-label="Margin Left"
                                    value={activeInputs.margin.left}
                                    onChange={(v) => handleSpacingChange('margin', 'left', v)}
                                    onBlur={handleInputBlur}
                                />
                            </div>
                        </div>

                        <div>
                            <h5 className="text-sm font-medium mb-2">Padding</h5>
                            <div className="grid grid-cols-2 gap-2">
                                <JollyTextField
                                    label="Top"
                                    aria-label="Padding Top"
                                    value={activeInputs.padding.top}
                                    onChange={(v) => handleSpacingChange('padding', 'top', v)}
                                    onBlur={handleInputBlur}
                                />
                                <JollyTextField
                                    label="Right"
                                    aria-label="Padding Right"
                                    value={activeInputs.padding.right}
                                    onChange={(v) => handleSpacingChange('padding', 'right', v)}
                                    onBlur={handleInputBlur}
                                />
                                <JollyTextField
                                    label="Bottom"
                                    aria-label="Padding Bottom"
                                    value={activeInputs.padding.bottom}
                                    onChange={(v) => handleSpacingChange('padding', 'bottom', v)}
                                    onBlur={handleInputBlur}
                                />
                                <JollyTextField
                                    label="Left"
                                    aria-label="Padding Left"
                                    value={activeInputs.padding.left}
                                    onChange={(v) => handleSpacingChange('padding', 'left', v)}
                                    onBlur={handleInputBlur}
                                />
                            </div>
                        </div>
                    </div>
                </DisclosurePanel>
            </Disclosure>

            {/* Typography */}
            <Disclosure defaultExpanded className="px-2 border-0 border-b border-b-secondary">
                <DisclosureHeader className="font-semibold flex items-center gap-2">
                    <Type className="h-4 w-4" />
                    Typography
                </DisclosureHeader>
                <DisclosurePanel className="pl-3">
                    <div className="flex flex-col gap-3">
                        <JollySelect
                            label="Font Weight"
                            aria-label="Font Weight"
                            items={fontWeightOptions}
                            selectedKey={fontWeight}
                            onSelectionChange={(key) => handleSelectChange(key, 'fontWeight', 'fontWeight')}
                        >
                            {(item) => <SelectItem id={item.id}>{item.name}</SelectItem>}
                        </JollySelect>

                        <div className="flex flex-col gap-1">
                            <label className="text-sm font-medium">Font Size: {fontSizeValue}px</label>
                            <Slider
                                value={fontSizeValue}
                                defaultValue={fontSizeValue}
                                onChange={(value) => handleSliderChange(value as number, 'px', 'fontSize', 'fontSize')}
                                onChangeEnd={() => handleInputBlur()}
                                minValue={4}
                                maxValue={72}
                                step={1}
                                aria-label="Font Size"
                            >
                                <SliderTrack>
                                    <SliderFillTrack/>
                                </SliderTrack>
                                <SliderThumb/>
                            </Slider>
                        </div>

                        <div className="flex flex-col gap-1">
                            <label className="text-sm font-medium">Line Height: {lineHeightValue}</label>
                            <Slider
                                value={lineHeightValue}
                                defaultValue={lineHeightValue}
                                onChange={(value) => handleSliderChange(value as number, '', 'lineHeight', 'lineHeight')}
                                onChangeEnd={() => handleInputBlur()}
                                minValue={0.5}
                                maxValue={3}
                                step={0.1}
                                aria-label="Line Height"
                            >
                                <SliderTrack>
                                    <SliderFillTrack/>
                                </SliderTrack>
                                <SliderThumb/>
                            </Slider>
                        </div>

                        <HexColorPicker
                            label="Text Color"
                            aria-label="Text Color"
                            value={color}
                            onChange={(v) => {
                                debouncedColorChange('color', v)
                            }}
                            onBlur={handleInputBlur}
                        />

                        <JollySelect
                            label="Font Style"
                            aria-label="Font Style"
                            items={fontStyleOptions}
                            selectedKey={fontStyle}
                            onSelectionChange={(key) => handleSelectChange(key, 'fontStyle', 'fontStyle')}
                        >
                            {(item) => <SelectItem id={item.id}>{item.name}</SelectItem>}
                        </JollySelect>

                        <JollySelect
                            label="Text Align"
                            aria-label="Text Align"
                            items={textAlignOptions}
                            selectedKey={textAlign}
                            onSelectionChange={(key) => handleSelectChange(key, 'textAlign', 'textAlign')}
                        >
                            {(item) => <SelectItem id={item.id}>{item.name}</SelectItem>}
                        </JollySelect>
                    </div>
                </DisclosurePanel>
            </Disclosure>

            {/* Appearance */}
            <Disclosure className="px-2 border-0 border-b border-b-secondary">
                <DisclosureHeader className="font-semibold flex items-center gap-2">
                    <Eye className="h-4 w-4" />
                    Appearance
                </DisclosureHeader>
                <DisclosurePanel className="pl-3">
                    <div className="flex flex-col gap-3">
                        <HexColorPicker
                            label="Background Color"
                            aria-label="Background Color"
                            value={backgroundColor}
                            onChange={(v) => {
                                debouncedColorChange('backgroundColor', v)
                            }}
                            onBlur={handleInputBlur}
                        />

                        <div className="flex flex-col gap-1">
                            <label className="text-sm font-medium">Border Radius: {borderRadiusValue}px</label>
                            <Slider<number>
                                value={borderRadiusValue}
                                defaultValue={borderRadiusValue}
                                onChange={(value) => handleSliderChange(value, 'px', 'borderRadius', 'borderRadius')}
                                onChangeEnd={() => handleInputBlur()}
                                minValue={0}
                                maxValue={50}
                                step={1}
                                aria-label="Border Radius"
                            >
                                <SliderTrack>
                                    <SliderFillTrack/>
                                </SliderTrack>
                                <SliderThumb/>
                            </Slider>
                        </div>

                        <div className="flex flex-col gap-1">
                            <label className="text-sm font-medium">Border Width: {borderWidthValue}px</label>
                            <Slider<number>
                                value={borderWidthValue}
                                defaultValue={borderWidthValue}
                                onChange={(value) => {
                                    handleSliderChange(value, 'px', 'borderWidth', 'borderWidth')
                                    // Also set border style to solid if width > 0 and style is none
                                    if (value > 0 && selectedElement && (borderStyle === 'none' || !borderStyle)) {
                                        selectedElement.style.borderStyle = 'solid'
                                        styleValuesRef.current.borderStyle = 'solid'
                                        setRerender(prev => prev + 1) // Force re-render to update select
                                    }
                                }}
                                onChangeEnd={() => handleInputBlur()}
                                minValue={0}
                                maxValue={20}
                                step={1}
                                aria-label="Border Width"
                                isDisabled={borderStyle === 'none'}
                            >
                                <SliderTrack>
                                    <SliderFillTrack/>
                                </SliderTrack>
                                <SliderThumb/>
                            </Slider>
                        </div>

                        <JollySelect
                            label="Border Style"
                            aria-label="Border Style"
                            items={borderStyleOptions}
                            selectedKey={borderStyle}
                            onSelectionChange={(key) => {
                                handleSelectChange(key as Key, 'borderStyle', 'borderStyle')
                                if (selectedElement) {
                                    // If style is set to none, set width to 0
                                    if (key === 'none') {
                                        handleSliderChange(0, 'px', 'borderWidth', 'borderWidth')
                                    }
                                    // If style is changed from none to something else, set a default width if it's 0
                                    else if (borderStyle === 'none' && borderWidthValue === 0) {
                                        handleSliderChange(1, 'px', 'borderWidth', 'borderWidth')
                                    }
                                }
                            }}
                        >
                            {(item) => <SelectItem id={item.id}>{item.name}</SelectItem>}
                        </JollySelect>

                        <HexColorPicker
                            label="Border Color"
                            aria-label="Border Color"
                            value={borderColor}
                            onChange={(v) => {
                                debouncedColorChange('borderColor', v)
                            }}
                            onBlur={handleInputBlur}
                        />
                    </div>
                </DisclosurePanel>
            </Disclosure>

            {/* Effects */}
            <Disclosure className="px-2 border-0 border-b border-b-secondary">
                <DisclosureHeader className="font-semibold flex items-center gap-2">
                    <Zap className="h-4 w-4" />
                    Effects
                </DisclosureHeader>
                <DisclosurePanel className="pl-3">
                    <div className="flex flex-col gap-3">
                        <div>
                            <h5 className="text-sm font-medium mb-2">Box Shadow</h5>
                            <div className="flex flex-col gap-3">
                                <JollySelect
                                    label="Shadow Position"
                                    aria-label="Shadow Position"
                                    items={boxShadowPositionOptions}
                                    selectedKey={boxShadowPosition || 'outset'}
                                    onSelectionChange={(key) => handleBoxShadowChange('position', key as string)}
                                >
                                    {(item) => <SelectItem id={item.id}>{item.name}</SelectItem>}
                                </JollySelect>

                                <div className="grid grid-cols-2 gap-2">
                                    <div className="flex flex-col gap-1">
                                        <label className="text-sm font-medium">Offset X: {boxShadowOffsetX}px</label>
                                        <Slider
                                            value={boxShadowOffsetX}
                                            defaultValue={boxShadowOffsetX}
                                            onChange={(value) => handleBoxShadowChange('offsetX', value as number)}
                                            minValue={-50}
                                            maxValue={50}
                                            step={1}
                                            aria-label="Shadow Offset X"
                                        >
                                            <SliderTrack>
                                                <SliderFillTrack/>
                                            </SliderTrack>
                                            <SliderThumb/>
                                        </Slider>
                                    </div>

                                    <div className="flex flex-col gap-1">
                                        <label className="text-sm font-medium">Offset Y: {boxShadowOffsetY}px</label>
                                        <Slider
                                            value={boxShadowOffsetY}
                                            defaultValue={boxShadowOffsetY}
                                            onChange={(value) => handleBoxShadowChange('offsetY', value as number)}
                                            minValue={-50}
                                            maxValue={50}
                                            step={1}
                                            aria-label="Shadow Offset Y"
                                        >
                                            <SliderTrack>
                                                <SliderFillTrack/>
                                            </SliderTrack>
                                            <SliderThumb/>
                                        </Slider>
                                    </div>
                                </div>

                                <div className="grid grid-cols-2 gap-2">
                                    <div className="flex flex-col gap-1">
                                        <label className="text-sm font-medium">Blur: {boxShadowBlur}px</label>
                                        <Slider
                                            value={boxShadowBlur}
                                            defaultValue={boxShadowBlur}
                                            onChange={(value) => handleBoxShadowChange('blur', value as number)}
                                            minValue={0}
                                            maxValue={50}
                                            step={1}
                                            aria-label="Shadow Blur"
                                        >
                                            <SliderTrack>
                                                <SliderFillTrack/>
                                            </SliderTrack>
                                            <SliderThumb/>
                                        </Slider>
                                    </div>

                                    <div className="flex flex-col gap-1">
                                        <label className="text-sm font-medium">Spread: {boxShadowSpread}px</label>
                                        <Slider
                                            value={boxShadowSpread}
                                            defaultValue={boxShadowSpread}
                                            onChange={(value) => handleBoxShadowChange('spread', value as number)}
                                            minValue={-20}
                                            maxValue={20}
                                            step={1}
                                            aria-label="Shadow Spread"
                                        >
                                            <SliderTrack>
                                                <SliderFillTrack/>
                                            </SliderTrack>
                                            <SliderThumb/>
                                        </Slider>
                                    </div>
                                </div>

                                <HexColorPicker
                                    label="Shadow Color"
                                    aria-label="Shadow Color"
                                    value={boxShadowColor}
                                    onChange={(v) => {
                                        debouncedColorChange('boxShadowColor', v)
                                    }}
                                    onBlur={handleInputBlur}
                                />
                            </div>
                        </div>
                    </div>
                </DisclosurePanel>
            </Disclosure>
        </div>
    )
}

export default StylesTab
